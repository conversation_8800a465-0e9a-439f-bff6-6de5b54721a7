{"version": 2, "name": "chefmate", "buildCommand": "echo 'No build needed for static site'", "outputDirectory": ".", "functions": {"api/spoonacular.js": {"maxDuration": 30}, "api/gemini.js": {"maxDuration": 30}}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}]}, {"source": "/assets/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}]}