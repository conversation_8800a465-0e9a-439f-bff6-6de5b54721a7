<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ChefMate Demo - Week 5 Deliverables</title>
    <link rel="stylesheet" href="assets/css/styles.css">
    <style>
        .demo-container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 2rem;
        }
        .demo-section {
            background: white;
            margin: 2rem 0;
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin: 2rem 0;
        }
        .feature-demo {
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            padding: 1.5rem;
            text-align: center;
        }
        .feature-demo.completed {
            border-color: #4caf50;
            background: #e8f5e8;
        }
        .status-badge {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }
        .status-completed {
            background: #4caf50;
            color: white;
        }
        .status-planned {
            background: #ffeb3b;
            color: #424242;
        }
        .demo-links {
            display: flex;
            gap: 1rem;
            justify-content: center;
            margin-top: 2rem;
        }
        .screenshot-placeholder {
            width: 100%;
            height: 200px;
            background: #f5f5f5;
            border: 2px dashed #ddd;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #757575;
            margin: 1rem 0;
            border-radius: 8px;
        }
        .color-palette {
            display: flex;
            gap: 1rem;
            margin: 1rem 0;
        }
        .color-swatch {
            width: 60px;
            height: 60px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.75rem;
            font-weight: 600;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <header style="text-align: center; margin-bottom: 3rem;">
            <h1 style="color: #4caf50; font-size: 3rem; margin-bottom: 0.5rem;">ChefMate</h1>
            <p style="font-size: 1.25rem; color: #757575;">Week 5 Deliverables Demo</p>
            <p style="color: #424242;">Smart Recipe Generator - HTML, CSS, and API Integration</p>
        </header>

        <!-- Project Overview -->
        <div class="demo-section">
            <h2>📋 Week 5 Requirements Status</h2>
            <div class="demo-grid">
                <div class="feature-demo completed">
                    <span class="status-badge status-completed">✅ COMPLETED</span>
                    <h3>HTML Structure</h3>
                    <p>4 main pages with semantic HTML, forms, and templates</p>
                </div>
                <div class="feature-demo completed">
                    <span class="status-badge status-completed">✅ COMPLETED</span>
                    <h3>CSS Styling</h3>
                    <p>Color scheme, typography, responsive design</p>
                </div>
                <div class="feature-demo completed">
                    <span class="status-badge status-completed">✅ COMPLETED</span>
                    <h3>API Integration</h3>
                    <p>Spoonacular API with error handling</p>
                </div>
            </div>
        </div>

        <!-- Color Scheme Demo -->
        <div class="demo-section">
            <h2>🎨 Color Scheme Implementation</h2>
            <p>Implemented the exact color scheme from your specification:</p>
            <div class="color-palette">
                <div class="color-swatch" style="background: #fafafa; color: #424242; border: 1px solid #ddd;">
                    #fafafa<br>Background
                </div>
                <div class="color-swatch" style="background: #4caf50;">
                    #4caf50<br>Actions
                </div>
                <div class="color-swatch" style="background: #ff7043;">
                    #ff7043<br>Favorites
                </div>
                <div class="color-swatch" style="background: #ffeb3b; color: #424242;">
                    #ffeb3b<br>AI Tips
                </div>
                <div class="color-swatch" style="background: #424242;">
                    #424242<br>Text
                </div>
            </div>
        </div>

        <!-- Pages Demo -->
        <div class="demo-section">
            <h2>📄 Pages Implemented</h2>
            <div class="demo-grid">
                <div class="feature-demo completed">
                    <h3>🏠 Home Page</h3>
                    <div class="screenshot-placeholder">Ingredient Input Interface</div>
                    <p>Hero section, ingredient form, dietary preferences, quick suggestions</p>
                    <a href="index.html" class="btn btn-primary">View Home</a>
                </div>
                <div class="feature-demo completed">
                    <h3>🔍 Search Results</h3>
                    <div class="screenshot-placeholder">Recipe Cards Grid</div>
                    <p>Recipe cards, filters, AI tips, pagination</p>
                    <a href="search.html" class="btn btn-primary">View Search</a>
                </div>
                <div class="feature-demo completed">
                    <h3>📖 Recipe Details</h3>
                    <div class="screenshot-placeholder">Recipe Information</div>
                    <p>Detailed recipe view, ingredients, instructions, nutrition</p>
                    <a href="recipe.html" class="btn btn-primary">View Recipe</a>
                </div>
                <div class="feature-demo completed">
                    <h3>❤️ Favorites</h3>
                    <div class="screenshot-placeholder">Saved Recipes</div>
                    <p>Saved recipes, personal notes, collections</p>
                    <a href="favorites.html" class="btn btn-primary">View Favorites</a>
                </div>
            </div>
        </div>

        <!-- Technical Features -->
        <div class="demo-section">
            <h2>⚙️ Technical Implementation</h2>
            <div class="demo-grid">
                <div class="feature-demo completed">
                    <span class="status-badge status-completed">✅ IMPLEMENTED</span>
                    <h3>Responsive Design</h3>
                    <p>Mobile-first approach with CSS Grid and Flexbox</p>
                </div>
                <div class="feature-demo completed">
                    <span class="status-badge status-completed">✅ IMPLEMENTED</span>
                    <h3>API Integration</h3>
                    <p>Spoonacular API with rate limiting and error handling</p>
                </div>
                <div class="feature-demo completed">
                    <span class="status-badge status-completed">✅ IMPLEMENTED</span>
                    <h3>Local Storage</h3>
                    <p>Favorites, ingredient history, user preferences</p>
                </div>
                <div class="feature-demo completed">
                    <span class="status-badge status-completed">✅ IMPLEMENTED</span>
                    <h3>Form Validation</h3>
                    <p>Real-time ingredient validation and feedback</p>
                </div>
            </div>
        </div>

        <!-- Typography Demo -->
        <div class="demo-section">
            <h2>📝 Typography Implementation</h2>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 2rem;">
                <div>
                    <h3 style="font-family: 'Poppins', sans-serif; margin-bottom: 0.5rem;">Poppins Headers</h3>
                    <p style="color: #757575; margin: 0;">Used for all headings (h1-h6)</p>
                </div>
                <div>
                    <p style="font-family: 'Open Sans', sans-serif; margin-bottom: 0.5rem; font-weight: 600;">Open Sans Body</p>
                    <p style="color: #757575; margin: 0;">Used for body text and UI elements</p>
                </div>
                <div>
                    <code style="font-family: 'Roboto Mono', monospace; background: #f5f5f5; padding: 0.5rem; border-radius: 4px;">Roboto Mono</code>
                    <p style="color: #757575; margin: 0.5rem 0 0 0;">Used for notes and code</p>
                </div>
            </div>
        </div>

        <!-- API Features -->
        <div class="demo-section">
            <h2>🔌 API Integration Features</h2>
            <div class="demo-grid">
                <div class="feature-demo completed">
                    <h3>Recipe Search</h3>
                    <p>Search by ingredients with dietary filters</p>
                    <code style="font-size: 0.75rem;">searchRecipesByIngredients()</code>
                </div>
                <div class="feature-demo completed">
                    <h3>Recipe Details</h3>
                    <p>Detailed information including nutrition</p>
                    <code style="font-size: 0.75rem;">getRecipeInformation()</code>
                </div>
                <div class="feature-demo completed">
                    <h3>AI Cooking Tips</h3>
                    <p>Gemini AI integration with fallback</p>
                    <code style="font-size: 0.75rem;">getAICookingTips()</code>
                </div>
                <div class="feature-demo completed">
                    <h3>Error Handling</h3>
                    <p>Rate limiting, network errors, graceful fallbacks</p>
                    <code style="font-size: 0.75rem;">handleAPIError()</code>
                </div>
            </div>
        </div>

        <!-- Testing -->
        <div class="demo-section">
            <h2>🧪 Testing & Setup</h2>
            <p>Use these tools to test the application:</p>
            <div class="demo-links">
                <a href="test-api.html" class="btn btn-secondary">API Test Page</a>
                <a href="index.html" class="btn btn-primary">Start Demo</a>
                <a href="README.md" class="btn btn-outline" target="_blank">Read Documentation</a>
            </div>
            <div style="background: #f5f5f5; padding: 1.5rem; border-radius: 8px; margin-top: 2rem;">
                <h4>Quick Setup:</h4>
                <ol style="margin: 0;">
                    <li>Get a free Spoonacular API key from <a href="https://spoonacular.com/food-api" target="_blank">spoonacular.com</a></li>
                    <li>Open browser console and run: <code>localStorage.setItem('chefmate_spoonacular_key', 'YOUR_KEY')</code></li>
                    <li>Start with the <a href="index.html">home page</a> and enter ingredients like "chicken, rice, vegetables"</li>
                    <li>Explore the search results and recipe details</li>
                </ol>
            </div>
        </div>

        <!-- Next Steps -->
        <div class="demo-section">
            <h2>🚀 Next Steps (Week 6 & 7)</h2>
            <div class="demo-grid">
                <div class="feature-demo">
                    <span class="status-badge status-planned">📅 PLANNED</span>
                    <h3>Enhanced Input</h3>
                    <p>Voice input and image recognition for ingredients</p>
                </div>
                <div class="feature-demo">
                    <span class="status-badge status-planned">📅 PLANNED</span>
                    <h3>User Profiles</h3>
                    <p>Personal accounts and preference management</p>
                </div>
                <div class="feature-demo">
                    <span class="status-badge status-planned">📅 PLANNED</span>
                    <h3>Advanced Features</h3>
                    <p>Shopping lists, cooking timers, meal planning</p>
                </div>
                <div class="feature-demo">
                    <span class="status-badge status-planned">📅 PLANNED</span>
                    <h3>Deployment</h3>
                    <p>Final testing, optimization, and deployment</p>
                </div>
            </div>
        </div>

        <footer style="text-align: center; margin-top: 3rem; padding: 2rem; border-top: 1px solid #e0e0e0;">
            <p style="color: #757575; margin: 0;">
                ChefMate - Week 5 Deliverables Complete ✅<br>
                <small>HTML Structure • CSS Styling • Spoonacular API Integration</small>
            </p>
        </footer>
    </div>
</body>
</html>
