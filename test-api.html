<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ChefMate API Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
            background-color: #fafafa;
        }
        .test-section {
            background: white;
            padding: 1.5rem;
            margin: 1rem 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .btn {
            background: #4caf50;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 4px;
            cursor: pointer;
            margin: 0.5rem;
        }
        .btn:hover {
            background: #388e3c;
        }
        .result {
            background: #f5f5f5;
            padding: 1rem;
            border-radius: 4px;
            margin-top: 1rem;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
        .error {
            background: #ffebee;
            color: #c62828;
            border: 1px solid #ef5350;
        }
        .success {
            background: #e8f5e8;
            color: #2e7d32;
            border: 1px solid #4caf50;
        }
        input, textarea {
            width: 100%;
            padding: 0.5rem;
            margin: 0.5rem 0;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>ChefMate API Test Page</h1>
    <p>This page helps you test the API integration before using the main application.</p>

    <!-- API Status Check -->
    <div class="test-section">
        <h2>1. API Status Check</h2>
        <p id="apiStatusText">Checking API configuration...</p>
        <button class="btn" onclick="checkAPIStatus()">Check API Status</button>
        <div id="apiStatusResult" class="result" style="display: none;"></div>

        <!-- Fallback for local development -->
        <div id="localDevSection" style="display: none; margin-top: 1rem; padding: 1rem; background: #fff3e0; border-radius: 8px;">
            <h4>Local Development Mode</h4>
            <p>For local testing, you can still set API keys manually:</p>
            <input type="text" id="apiKeyInput" placeholder="Enter your Spoonacular API key">
            <button class="btn" onclick="setApiKey()">Set API Key</button>
        </div>
    </div>

    <!-- Test Recipe Search -->
    <div class="test-section">
        <h2>2. Test Recipe Search</h2>
        <p>Test searching for recipes by ingredients:</p>
        <input type="text" id="ingredientsInput" placeholder="Enter ingredients (e.g., chicken, rice, tomatoes)" value="chicken, rice, tomatoes">
        <button class="btn" onclick="testRecipeSearch()">Search Recipes</button>
        <div id="searchResult" class="result" style="display: none;"></div>
    </div>

    <!-- Test Recipe Details -->
    <div class="test-section">
        <h2>3. Test Recipe Details</h2>
        <p>Test getting detailed recipe information:</p>
        <input type="number" id="recipeIdInput" placeholder="Enter recipe ID (e.g., 715538)" value="715538">
        <button class="btn" onclick="testRecipeDetails()">Get Recipe Details</button>
        <div id="detailsResult" class="result" style="display: none;"></div>
    </div>

    <!-- Test AI Integration -->
    <div class="test-section">
        <h2>4. Test AI Integration (Optional)</h2>
        <p>Test AI cooking tips (requires Gemini API key):</p>
        <input type="text" id="geminiKeyInput" placeholder="Enter your Gemini API key (optional)">
        <button class="btn" onclick="setGeminiKey()">Set Gemini Key</button>
        <button class="btn" onclick="testAITips()">Test AI Tips</button>
        <div id="aiResult" class="result" style="display: none;"></div>
    </div>

    <!-- Local Storage Test -->
    <div class="test-section">
        <h2>5. Test Local Storage</h2>
        <p>Test favorites and local storage functionality:</p>
        <button class="btn" onclick="testLocalStorage()">Test Local Storage</button>
        <button class="btn" onclick="clearLocalStorage()">Clear Local Storage</button>
        <div id="storageResult" class="result" style="display: none;"></div>
    </div>

    <script src="assets/js/utils.js"></script>
    <script src="assets/js/api.js"></script>
    <script>
        function showResult(elementId, content, isError = false) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `result ${isError ? 'error' : 'success'}`;
            element.textContent = typeof content === 'object' ? JSON.stringify(content, null, 2) : content;
        }

        function checkAPIStatus() {
            const isVercel = window.location.hostname.includes('vercel.app');
            const isLocalhost = window.location.hostname === 'localhost';

            if (isVercel) {
                showResult('apiStatusResult',
                    'Running on Vercel with serverless functions.\n' +
                    'API keys are configured server-side.\n' +
                    'No user setup required!');
                document.getElementById('apiStatusText').textContent =
                    '✅ Vercel deployment detected - API keys handled server-side';
            } else if (isLocalhost) {
                showResult('apiStatusResult',
                    'Running locally. You can test with direct API calls.\n' +
                    'Set your API key below for testing.', true);
                document.getElementById('apiStatusText').textContent =
                    '⚠️ Local development - API key setup required';
                document.getElementById('localDevSection').style.display = 'block';
            } else {
                showResult('apiStatusResult',
                    'Custom deployment detected.\n' +
                    'Check if serverless functions are configured.', true);
                document.getElementById('localDevSection').style.display = 'block';
            }
        }

        function setApiKey() {
            const apiKey = document.getElementById('apiKeyInput').value.trim();
            if (!apiKey) {
                showResult('apiStatusResult', 'Please enter an API key', true);
                return;
            }

            window.ChefMateAPI.setApiKey('spoonacular', apiKey);
            showResult('apiStatusResult', 'API key set successfully! You can now test recipe search.');
        }

        async function testRecipeSearch() {
            const ingredients = document.getElementById('ingredientsInput').value.trim();
            if (!ingredients) {
                showResult('searchResult', 'Please enter some ingredients', true);
                return;
            }

            try {
                showResult('searchResult', 'Searching for recipes...');
                const ingredientArray = ingredients.split(',').map(i => i.trim());
                const results = await window.ChefMateAPI.searchRecipesByIngredients(ingredientArray, { number: 3 });
                
                if (results && results.length > 0) {
                    const summary = results.map(recipe => ({
                        id: recipe.id,
                        title: recipe.title,
                        usedIngredients: recipe.usedIngredientCount,
                        missedIngredients: recipe.missedIngredientCount,
                        image: recipe.image
                    }));
                    showResult('searchResult', `Found ${results.length} recipes:\n\n${JSON.stringify(summary, null, 2)}`);
                } else {
                    showResult('searchResult', 'No recipes found', true);
                }
            } catch (error) {
                showResult('searchResult', `Error: ${error.message}`, true);
            }
        }

        async function testRecipeDetails() {
            const recipeId = document.getElementById('recipeIdInput').value.trim();
            if (!recipeId) {
                showResult('detailsResult', 'Please enter a recipe ID', true);
                return;
            }

            try {
                showResult('detailsResult', 'Getting recipe details...');
                const details = await window.ChefMateAPI.getRecipeInformation(recipeId);
                
                const summary = {
                    id: details.id,
                    title: details.title,
                    readyInMinutes: details.readyInMinutes,
                    servings: details.servings,
                    vegetarian: details.vegetarian,
                    vegan: details.vegan,
                    glutenFree: details.glutenFree,
                    ingredientCount: details.extendedIngredients?.length || 0,
                    summary: details.summary?.substring(0, 200) + '...'
                };
                
                showResult('detailsResult', JSON.stringify(summary, null, 2));
            } catch (error) {
                showResult('detailsResult', `Error: ${error.message}`, true);
            }
        }

        function setGeminiKey() {
            const apiKey = document.getElementById('geminiKeyInput').value.trim();
            if (!apiKey) {
                showResult('aiResult', 'Please enter a Gemini API key', true);
                return;
            }

            window.ChefMateAPI.setApiKey('gemini', apiKey);
            showResult('aiResult', 'Gemini API key set successfully!');
        }

        async function testAITips() {
            try {
                showResult('aiResult', 'Getting AI cooking tips...');
                
                const mockRecipe = {
                    title: 'Chicken Fried Rice',
                    readyInMinutes: 30,
                    extendedIngredients: [
                        { name: 'chicken' },
                        { name: 'rice' },
                        { name: 'vegetables' }
                    ]
                };
                
                const tips = await window.ChefMateAPI.getAICookingTips(mockRecipe, ['chicken', 'rice', 'vegetables']);
                showResult('aiResult', JSON.stringify(tips, null, 2));
            } catch (error) {
                showResult('aiResult', `Error: ${error.message}`, true);
            }
        }

        function testLocalStorage() {
            try {
                // Test favorites
                const testRecipe = {
                    id: 12345,
                    title: 'Test Recipe',
                    image: 'test.jpg'
                };

                window.ChefMateUtils.Favorites.add(testRecipe);
                const favorites = window.ChefMateUtils.Favorites.getAll();
                
                // Test ingredient history
                window.ChefMateUtils.IngredientHistory.addToHistory(['chicken', 'rice']);
                const history = window.ChefMateUtils.IngredientHistory.getHistory();

                const result = {
                    favoritesCount: favorites.length,
                    historyCount: history.length,
                    testRecipeAdded: window.ChefMateUtils.Favorites.isFavorite(12345),
                    recentSearch: history[0]?.ingredients
                };

                showResult('storageResult', JSON.stringify(result, null, 2));
            } catch (error) {
                showResult('storageResult', `Error: ${error.message}`, true);
            }
        }

        function clearLocalStorage() {
            try {
                localStorage.clear();
                showResult('storageResult', 'Local storage cleared successfully!');
            } catch (error) {
                showResult('storageResult', `Error: ${error.message}`, true);
            }
        }

        // Show initial instructions
        document.addEventListener('DOMContentLoaded', () => {
            // Auto-check API status on load
            checkAPIStatus();

            // For local development, check for existing API key
            const currentKey = localStorage.getItem('chefmate_spoonacular_key');
            if (currentKey && currentKey !== 'YOUR_SPOONACULAR_API_KEY') {
                const apiKeyInput = document.getElementById('apiKeyInput');
                if (apiKeyInput) {
                    apiKeyInput.value = currentKey;
                }
            }
        });
    </script>
</body>
</html>
