/* ===== CSS RESET & BASE STYLES ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: 'Open Sans', sans-serif;
    background-color: #fafafa;
    color: #424242;
    line-height: 1.6;
    overflow-x: hidden;
}

/* ===== TYPOGRAPHY ===== */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Poppins', sans-serif;
    font-weight: 600;
    line-height: 1.3;
    margin-bottom: 0.5rem;
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.5rem; }
h4 { font-size: 1.25rem; }
h5 { font-size: 1.125rem; }
h6 { font-size: 1rem; }

p {
    margin-bottom: 1rem;
}

.text-mono {
    font-family: 'Roboto Mono', monospace;
}

/* ===== COLOR VARIABLES ===== */
:root {
    --bg-primary: #fafafa;
    --bg-white: #ffffff;
    --bg-light: #f5f5f5;
    --color-primary: #4caf50;
    --color-primary-dark: #388e3c;
    --color-primary-light: #81c784;
    --color-secondary: #ff7043;
    --color-accent: #ffeb3b;
    --color-text: #424242;
    --color-text-light: #757575;
    --color-text-dark: #212121;
    --color-border: #e0e0e0;
    --color-error: #f44336;
    --color-success: #4caf50;
    --color-warning: #ff9800;
    --shadow-light: 0 2px 4px rgba(0,0,0,0.1);
    --shadow-medium: 0 4px 8px rgba(0,0,0,0.15);
    --shadow-heavy: 0 8px 16px rgba(0,0,0,0.2);
    --border-radius: 8px;
    --border-radius-large: 12px;
    --transition: all 0.3s ease;
}

/* ===== LAYOUT UTILITIES ===== */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

.main {
    min-height: calc(100vh - 140px);
    padding-top: 80px;
}

/* ===== NAVIGATION ===== */
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: var(--bg-white);
    box-shadow: var(--shadow-light);
    z-index: 1000;
}

.nav {
    padding: 1rem 0;
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.nav-logo {
    width: 32px;
    height: 32px;
}

.nav-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--color-primary);
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: var(--color-text);
    font-weight: 500;
    transition: var(--transition);
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
}

.nav-link:hover,
.nav-link.active {
    color: var(--color-primary);
    background-color: var(--bg-light);
}

.nav-toggle {
    display: none;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.5rem;
}

.hamburger {
    display: block;
    width: 24px;
    height: 2px;
    background: var(--color-text);
    position: relative;
}

.hamburger::before,
.hamburger::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 2px;
    background: var(--color-text);
    transition: var(--transition);
}

.hamburger::before { top: -8px; }
.hamburger::after { top: 8px; }

/* ===== BUTTONS ===== */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: var(--border-radius);
    font-family: 'Open Sans', sans-serif;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition);
    font-size: 0.875rem;
    line-height: 1;
}

.btn-primary {
    background-color: var(--color-primary);
    color: white;
}

.btn-primary:hover {
    background-color: var(--color-primary-dark);
    transform: translateY(-1px);
    box-shadow: var(--shadow-medium);
}

.btn-secondary {
    background-color: var(--color-secondary);
    color: white;
}

.btn-secondary:hover {
    background-color: #e64a19;
    transform: translateY(-1px);
    box-shadow: var(--shadow-medium);
}

.btn-outline {
    background-color: transparent;
    color: var(--color-primary);
    border: 2px solid var(--color-primary);
}

.btn-outline:hover {
    background-color: var(--color-primary);
    color: white;
}

.btn-small {
    padding: 0.5rem 1rem;
    font-size: 0.75rem;
}

.btn-large {
    padding: 1rem 2rem;
    font-size: 1rem;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* ===== FORMS ===== */
.form-group {
    margin-bottom: 1.5rem;
}

.input-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--color-text-dark);
}

.form-input,
.form-textarea,
.ingredient-textarea,
.search-input,
.filter-select,
.time-select {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid var(--color-border);
    border-radius: var(--border-radius);
    font-family: 'Open Sans', sans-serif;
    font-size: 0.875rem;
    transition: var(--transition);
    background-color: var(--bg-white);
}

.form-input:focus,
.form-textarea:focus,
.ingredient-textarea:focus,
.search-input:focus,
.filter-select:focus,
.time-select:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
}

.ingredient-textarea {
    resize: vertical;
    min-height: 100px;
}

/* ===== CARDS ===== */
.card {
    background: var(--bg-white);
    border-radius: var(--border-radius-large);
    box-shadow: var(--shadow-light);
    padding: 1.5rem;
    transition: var(--transition);
}

.card:hover {
    box-shadow: var(--shadow-medium);
    transform: translateY(-2px);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--color-border);
}

/* ===== HERO SECTION ===== */
.hero {
    background: linear-gradient(135deg, var(--color-primary-light), var(--color-primary));
    color: white;
    padding: 4rem 0;
    text-align: center;
}

.hero-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.hero-subtitle {
    font-size: 1.25rem;
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto;
}

/* ===== INGREDIENT INPUT ===== */
.ingredient-input {
    padding: 3rem 0;
}

.input-card {
    background: var(--bg-white);
    border-radius: var(--border-radius-large);
    box-shadow: var(--shadow-medium);
    padding: 2rem;
    max-width: 800px;
    margin: 0 auto;
}

.input-title {
    text-align: center;
    margin-bottom: 2rem;
    color: var(--color-text-dark);
}

.ingredient-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.preferences-group {
    margin: 1.5rem 0;
}

.preferences-title {
    margin-bottom: 1rem;
    color: var(--color-text-dark);
}

.checkbox-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
}

.checkbox-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.checkbox-item:hover {
    background-color: var(--bg-light);
}

.checkbox-item input[type="checkbox"] {
    width: 18px;
    height: 18px;
    accent-color: var(--color-primary);
}

.time-group {
    margin: 1rem 0;
}

/* ===== LOADING STATES ===== */
.loading-state {
    text-align: center;
    padding: 4rem 2rem;
}

.loading-spinner {
    margin-bottom: 1rem;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--color-border);
    border-top: 4px solid var(--color-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ===== ERROR STATES ===== */
.error-state,
.no-results-state {
    text-align: center;
    padding: 4rem 2rem;
}

.error-icon,
.no-results-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
}

/* ===== FOOTER ===== */
.footer {
    background: var(--color-text-dark);
    color: white;
    padding: 2rem 0;
    margin-top: 4rem;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.footer-brand h3 {
    color: var(--color-primary);
    margin-bottom: 0.5rem;
}

.footer-links {
    display: flex;
    gap: 2rem;
}

.footer-link {
    color: white;
    text-decoration: none;
    transition: var(--transition);
}

.footer-link:hover {
    color: var(--color-primary);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }
    
    .nav-toggle {
        display: block;
    }
    
    .hero-title {
        font-size: 2rem;
    }
    
    .hero-subtitle {
        font-size: 1rem;
    }
    
    .container {
        padding: 0 0.5rem;
    }
    
    .input-card {
        padding: 1.5rem;
    }
    
    .checkbox-grid {
        grid-template-columns: 1fr;
    }
    
    .footer-content {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .footer-links {
        flex-wrap: wrap;
        justify-content: center;
    }
}

/* ===== QUICK SUGGESTIONS ===== */
.quick-suggestions {
    padding: 3rem 0;
    background: var(--bg-light);
}

.section-title {
    text-align: center;
    margin-bottom: 2rem;
    color: var(--color-text-dark);
}

.suggestions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-top: 2rem;
}

.suggestion-card {
    background: var(--bg-white);
    border: none;
    border-radius: var(--border-radius-large);
    padding: 1.5rem;
    text-align: center;
    cursor: pointer;
    transition: var(--transition);
    box-shadow: var(--shadow-light);
}

.suggestion-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-medium);
    background: var(--color-primary);
    color: white;
}

.suggestion-card h3 {
    margin-bottom: 0.5rem;
    color: inherit;
}

.suggestion-card p {
    margin: 0;
    opacity: 0.8;
    font-size: 0.875rem;
}

/* ===== FEATURES PREVIEW ===== */
.features-preview {
    padding: 4rem 0;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.feature-card {
    text-align: center;
    padding: 2rem 1rem;
}

.feature-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.feature-card h3 {
    margin-bottom: 1rem;
    color: var(--color-text-dark);
}

.feature-card p {
    color: var(--color-text-light);
    margin: 0;
}

/* ===== SEARCH RESULTS ===== */
.search-header {
    padding: 2rem 0;
    background: var(--bg-white);
    border-bottom: 1px solid var(--color-border);
}

.search-info {
    text-align: center;
    margin-bottom: 2rem;
}

.search-title {
    margin-bottom: 0.5rem;
}

.search-subtitle {
    color: var(--color-text-light);
    margin: 0;
}

.quick-search {
    max-width: 600px;
    margin: 0 auto;
}

.search-input-group {
    display: flex;
    gap: 1rem;
}

.search-input {
    flex: 1;
}

/* ===== FILTERS ===== */
.filters-section {
    padding: 1.5rem 0;
    background: var(--bg-light);
}

.filters-card {
    background: var(--bg-white);
    border-radius: var(--border-radius-large);
    padding: 1.5rem;
    box-shadow: var(--shadow-light);
}

.filters-title {
    margin-bottom: 1rem;
    color: var(--color-text-dark);
}

.filters-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.filter-label {
    font-weight: 600;
    color: var(--color-text-dark);
    font-size: 0.875rem;
}

/* ===== RECIPE CARDS ===== */
.results-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 2rem;
    padding: 2rem 0;
}

.recipe-card {
    background: var(--bg-white);
    border-radius: var(--border-radius-large);
    overflow: hidden;
    box-shadow: var(--shadow-light);
    transition: var(--transition);
    cursor: pointer;
}

.recipe-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-heavy);
}

.recipe-image-container {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.recipe-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.recipe-card:hover .recipe-image {
    transform: scale(1.05);
}

.favorite-btn {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: rgba(255, 255, 255, 0.9);
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
}

.favorite-btn:hover {
    background: var(--color-secondary);
    color: white;
    transform: scale(1.1);
}

.heart-icon {
    font-size: 1.25rem;
}

.recipe-badges {
    position: absolute;
    bottom: 1rem;
    left: 1rem;
    display: flex;
    gap: 0.5rem;
}

.badge {
    background: var(--color-accent);
    color: var(--color-text-dark);
    padding: 0.25rem 0.5rem;
    border-radius: var(--border-radius);
    font-size: 0.75rem;
    font-weight: 600;
}

.recipe-content {
    padding: 1.5rem;
}

.recipe-title {
    margin-bottom: 1rem;
    color: var(--color-text-dark);
    font-size: 1.25rem;
}

.recipe-meta {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
    font-size: 0.875rem;
    color: var(--color-text-light);
}

.recipe-time,
.recipe-difficulty,
.recipe-rating {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.time-icon {
    font-size: 1rem;
}

.difficulty-dots {
    display: flex;
    gap: 2px;
}

.difficulty-dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: var(--color-border);
}

.difficulty-dot.active {
    background: var(--color-primary);
}

.stars {
    color: var(--color-accent);
}

.recipe-description {
    color: var(--color-text-light);
    font-size: 0.875rem;
    margin-bottom: 1rem;
    line-height: 1.5;
}

.recipe-ingredients {
    margin-bottom: 1.5rem;
    font-size: 0.875rem;
}

.ingredients-label {
    font-weight: 600;
    color: var(--color-text-dark);
}

.ingredients-list {
    color: var(--color-text-light);
}

.recipe-actions {
    display: flex;
    gap: 0.5rem;
}

@media (max-width: 480px) {
    .hero {
        padding: 2rem 0;
    }

    .ingredient-input {
        padding: 2rem 0;
    }

    .btn-large {
        width: 100%;
    }

    .results-grid {
        grid-template-columns: 1fr;
    }

    .filters-grid {
        grid-template-columns: 1fr;
    }

    .search-input-group {
        flex-direction: column;
    }

    .recipe-actions {
        flex-direction: column;
    }
}

/* ===== AI SUGGESTIONS ===== */
.ai-suggestions {
    padding: 2rem 0;
    background: var(--bg-light);
}

.ai-card {
    background: var(--color-accent);
    border-radius: var(--border-radius-large);
    padding: 2rem;
    box-shadow: var(--shadow-medium);
}

.ai-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.ai-icon {
    font-size: 2rem;
}

.ai-header h3 {
    margin: 0;
    color: var(--color-text-dark);
}

.ai-content {
    color: var(--color-text-dark);
}

.ai-tips-list {
    list-style: none;
    padding: 0;
}

.ai-tips-list li {
    padding: 0.75rem 0;
    border-bottom: 1px solid rgba(66, 66, 66, 0.1);
    position: relative;
    padding-left: 1.5rem;
}

.ai-tips-list li:before {
    content: '💡';
    position: absolute;
    left: 0;
    top: 0.75rem;
}

.ai-tips-list li:last-child {
    border-bottom: none;
}

/* ===== LOAD MORE SECTION ===== */
.load-more-section {
    text-align: center;
    padding: 2rem 0;
}

/* ===== VALIDATION MESSAGES ===== */
.validation-message {
    margin-top: 0.5rem;
    padding: 0.5rem;
    border-radius: var(--border-radius);
    font-size: 0.875rem;
    font-weight: 500;
}

.validation-success {
    background-color: #e8f5e8;
    color: var(--color-success);
    border: 1px solid var(--color-success);
}

.validation-warning {
    background-color: #fff3e0;
    color: var(--color-warning);
    border: 1px solid var(--color-warning);
}

.validation-error {
    background-color: #ffebee;
    color: var(--color-error);
    border: 1px solid var(--color-error);
}

/* ===== ERROR MESSAGES ===== */
.error-message {
    margin-top: 1rem;
    padding: 1rem;
    background-color: #ffebee;
    border: 1px solid var(--color-error);
    border-radius: var(--border-radius);
    color: var(--color-error);
}

.error-content {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.error-icon {
    font-size: 1.25rem;
}

.error-text {
    flex: 1;
}

.error-close {
    background: none;
    border: none;
    font-size: 1.25rem;
    cursor: pointer;
    color: var(--color-error);
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* ===== INGREDIENT HISTORY ===== */
.ingredient-history {
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid var(--color-border);
}

.ingredient-history h3 {
    margin-bottom: 1rem;
    color: var(--color-text-dark);
    font-size: 1.125rem;
}

.history-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.history-item {
    background: var(--bg-light);
    border: 1px solid var(--color-border);
    border-radius: var(--border-radius);
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    cursor: pointer;
    transition: var(--transition);
    color: var(--color-text);
}

.history-item:hover {
    background: var(--color-primary);
    color: white;
    border-color: var(--color-primary);
}

/* ===== FAVORITE BUTTON STATES ===== */
.favorite-btn.favorited {
    background: var(--color-secondary);
    color: white;
}

.favorite-btn.favorited:hover {
    background: #e64a19;
}

/* ===== MOBILE NAVIGATION ACTIVE STATES ===== */
@media (max-width: 768px) {
    .nav-menu.active {
        display: flex;
        flex-direction: column;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: var(--bg-white);
        box-shadow: var(--shadow-medium);
        padding: 1rem;
        gap: 0.5rem;
    }

    .nav-toggle.active .hamburger {
        background: transparent;
    }

    .nav-toggle.active .hamburger::before {
        transform: rotate(45deg);
        top: 0;
    }

    .nav-toggle.active .hamburger::after {
        transform: rotate(-45deg);
        top: 0;
    }
}

/* ===== RESULTS SECTION ===== */
.results-section {
    min-height: 400px;
    padding: 2rem 0;
}

/* ===== TEMPLATE STYLES ===== */
template {
    display: none;
}
